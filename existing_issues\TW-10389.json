{"Key": "TW-10389", "Id": "50599", "Summary": "app o api-4: <PERSON><PERSON><PERSON> inte booleanish som cancelled till PUT /v4/scheduled-staff", "Description": "h1. Bakgrund\n\n{{cancelled}} skall enligt dokumentationen vara en datetime i PUT /v4/scheduled-staff/{id}. However, detta var uppfuckat och den var istället en booleanish. Detta fixades i [https://timewaveab.atlassian.net/browse/TW-10388|https://timewaveab.atlassian.net/browse/TW-10388|smart-link].\n\nh1. Problem\n\nFör att inte ha sönder bakåtkompabilteten gjordes fixen så att API-4 äter både booleanish och datetime.\n\nh1. Acceptanskriterier\n\n* Mobilappen skickar alltid datetime eller null som cancelled på PUT /v4/scheduled-staff/{id}.\n* API-4 accepterar inte längre booleanish-värden på cancelled på PUT /v4/scheduled-staff/{id}.", "Assignee": null, "Reporter": "<PERSON><PERSON><PERSON>", "Labels": ["after-summer-2", "api-4", "app", "approved-not-merged"], "CreateDate": "2025-05-28T15:37:06.336+02:00", "UpdateDate": "2025-07-14T14:02:57.752+02:00", "Status": "In Review", "ParentIssueKey": "TW-10664", "IssueType": "Bug", "Priority": null, "ResolutionDate": null, "Resolution": null, "Links": [{"LinkType": "Problem/Incident", "InwardIssueKey": "TW-10388", "OutwardIssueKey": "TW-10389"}], "Comments": [{"Id": "70211", "AuthorUser": "<PERSON>", "Body": "Appen gör ingen PUT mot /v4/scheduled-staff/{id} ", "GroupLevel": null, "CreatedDate": "2025-07-02T10:12:46.2+02:00"}], "Attachments": []}