{"Key": "TW-10443", "Id": "50885", "Summary": "Byt query parameter tenantIds till tenant-ids", "Description": "h1. Bakgrund\n\nVåra queryparametrar i 4:e generationens tjänster skall vara kebab-case för att följa URL-konvention.\n\nh1. Problem\n\nDetta efterlevs inte när det kommer till tenantIds, och kanske någon till.\n\nh1. Lösning\n\nEftersom detta kan påverka mobilappen, gö<PERSON> en kontroll så att mobilappen och TWU4 inte använder någon av parametrarna som ändrats på. OM mobilappen gör detta, måste detta ärende göras i tre steg:\n\n# Lägg in stöd för tenant-ids parallelt med tenantIds, och motsvarande för andra eventuella camelCase-parametrar.\n# Ändra mobilappen och TWU4 till att använda kebab-case överallt på URL-parametrar till API-4 (inkl core-4, auth-4)\n# Ta bort stödet för tenantIds och andra eventuella camelCase-parametrar.\n\nh1. Acceptanskriterier\n\n* Alla camelCase-query-parametrar i 4:e generationens API-tjänster skall vara bytta till kebab-case.", "Assignee": "<PERSON>", "Reporter": "<PERSON><PERSON><PERSON>", "Labels": ["after-summer-2", "api-4", "app", "approved-not-merged", "auth-4", "tenant-web-ui-4"], "CreateDate": "2025-06-05T16:27:50.463+02:00", "UpdateDate": "2025-07-14T16:09:45.198+02:00", "Status": "In Review", "ParentIssueKey": "TW-10664", "IssueType": "Feature", "Priority": null, "ResolutionDate": null, "Resolution": null, "Links": [], "Comments": [], "Attachments": []}