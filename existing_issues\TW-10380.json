{"Key": "TW-10380", "Id": "50556", "Summary": "Kundportalen: <PERSON><PERSON><PERSON><PERSON> bok<PERSON> \"utan anställd\"", "Description": "h1. Bakgrund\n\nVi har en Kundportal.\nI kundportalen ser kunderna alla kommande bokningar.\nKunderna ser bokningar oavsett om de är tilldelade en anställd, eller är ofördelade, dvs. ligger under “U<PERSON> anställd”.\n\nh1. Problem\n\nVissa av våra kunder vill inte visa bokningar som ligger på Utan anställd för våra kunder. Anledningen till detta är att alla bokningar som ligger under “Utan anställd” är att betrakta som preliminära, och kan även ligga på preliminära datum och tider. Det blir då tokigt att visa dessa boknignar för slutkund.\n\nh1. Lösning\n\nMedge att våra kunder kan välja att inte visa bokningar i Kundportalen om dessa är kopplade till “Utan anställd”.\n\nFörslagsvis under rubrik “Bokningar Utan anställd” under befintliga “Bokningar”.\n\n!Skärmavbild 2025-05-27 kl. 17.35.27.png|width=376,height=473,alt=\"Skärmavbild 2025-05-27 kl. 17.35.27.png\"!\n\nLägg ihop med “Bokningar”\n\nh1. \n*Acceptanskriterier*\n\nDet finns i inställningarna för kundportalen en inställning “Visa bokningar utan anställd”, som by default är TRUE för alla nuvarande kunder\n\nOm inställningen är TRUE, ska ingen förändring mot nu ske\n\nOm inställningen är FALSE, ska bokningar utan anställd inte visas för slutkund i kundportalen", "Assignee": "<PERSON>", "Reporter": "anders.backman", "Labels": ["after-summer-2", "approved-not-merged", "kundportalen"], "CreateDate": "2025-05-27T17:27:54.749+02:00", "UpdateDate": "2025-07-11T15:57:34.886+02:00", "Status": "In Review", "ParentIssueKey": "TW-9429", "IssueType": "Feature", "Priority": null, "ResolutionDate": null, "Resolution": null, "Links": [{"LinkType": "<PERSON><PERSON><PERSON>", "InwardIssueKey": "TW-10380", "OutwardIssueKey": "TW-10343"}, {"LinkType": "<PERSON><PERSON><PERSON>", "InwardIssueKey": "TW-10439", "OutwardIssueKey": "TW-10380"}, {"LinkType": "Blocks", "InwardIssueKey": "TW-10598", "OutwardIssueKey": "TW-10380"}, {"LinkType": "Blocks", "InwardIssueKey": "TW-10579", "OutwardIssueKey": "TW-10380"}], "Comments": [{"Id": "69982", "AuthorUser": "<PERSON>", "Body": "Chainad PR på [https://timewaveab.atlassian.net/browse/TW-10579|https://timewaveab.atlassian.net/browse/TW-10579|smart-link]. ", "GroupLevel": null, "CreatedDate": "2025-06-26T16:20:07.123+02:00"}], "Attachments": [{"Id": "35887", "AuthorUser": "anders.backman", "CreatedDate": "2025-05-27T17:37:02.236+02:00", "FileName": "Skärmavbild 2025-05-27 kl. 17.35.27.png", "MimeType": "image/png", "FileSize": 135373, "ContentUri": "https://timewaveab.atlassian.net/secure/attachment/35887/Skärmavbild 2025-05-27 kl. 17.35.27.png"}]}