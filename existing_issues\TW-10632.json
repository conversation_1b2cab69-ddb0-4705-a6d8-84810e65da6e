{"Key": "TW-10632", "Id": "51843", "Summary": "P2: Kan inte radera SMS/oklar SMS-status", "Description": "{panel:bgColor=#deebff}\nh4. HubSpot linked tickets:\n\n* [SMS kan ej raderas från SMS-vyn. (Ticket ID: 25990626403)|https://app.hubspot.com/contacts/7709646/ticket/25990626403]\n\n~_(please, do not edit or duplicate in description)_~\n{panel}\n\nh1. Bakgrund\n\nMan kan i tenant-web-ui-3 skapa SMS, och man kan i vissa fall också radera dem.\n\nh1. Rapporterad av\n\nOla@SHOS AB (Svenska hem och städ)\n\n[https://svenskahemochstad.timewave.se/messages/sms|https://svenskahemochstad.timewave.se/messages/sms]\n\nh1. Förväntat beteende\n\nNär man raderar ett SMS i [Meddelanden / SMS|https://svenskahemochstad.timewave.se/messages/sms] i twu-3, tas det bort och skickas inte.\n\nh1. Reproduktionssteg\n\nObligatoriskt om det inte väldigt tydligt framgår i “Förväntat beteende” ovan.\n\nSMS från innan NOW() (2025-07-03 10:53)\n\n# Gå till [https://svenskahemochstad.timewave.se/messages/sms|https://svenskahemochstad.timewave.se/messages/sms]\n# Filtrera på nummer 0046702474858\n# Markera SMS med {{Skickad}} 2025-07-03 10:10 och {{Status}} Ej skickad (i kö)\n# Klicka på radera (rött minus)\n# Sidan laddar om, men inget verkar skett\n\nSMS efter NOW() (2025-07-03 10:53)\n\n# Gå till [https://svenskahemochstad.timewave.se/messages/sms|https://svenskahemochstad.timewave.se/messages/sms]\n# Filtrera på nummer 0046725030458\n# Markera SMS med {{Skickad}} 2025-07-03 13:40 och {{Status}} Ej skickad (i kö)\n# Klicka på radera (rött minus)\n# Sidan laddar om, men inget verkar skett\n\n\n\nh1. Miljö\n\n* System\n** tenant-web-ui-3\n** sms\n\nh1. Screenshots/inspelningar\n\n!Screen Recording 2025-07-03 at 11.00.09.mov|width=1204,height=944,alt=\"Screen Recording 2025-07-03 at 11.00.09.mov\"!\n\n\n\nh1. Lösning\n\nGör så att SMSen raderas, alternativt att om de redan skickats, får korrekt status.\n\nÖnskat är att de två SMSen ovan raderas och inte skickas", "Assignee": "<PERSON>", "Reporter": "<PERSON>", "Labels": ["after-summer-2", "approved-not-merged"], "CreateDate": "2025-07-03T10:47:10.167+02:00", "UpdateDate": "2025-07-15T08:39:24.03+02:00", "Status": "In Review", "ParentIssueKey": "TW-10624", "IssueType": "Bug", "Priority": null, "ResolutionDate": null, "Resolution": null, "Links": [], "Comments": [{"Id": "70309", "AuthorUser": "<PERSON>", "Body": "SMS servicen verkar göra sin grej korrekt. Har kört anropet manuellt i Postman och får tillbaka “deleted” och de tänkta ändringarna när detta sker i sms_data.list slår också igenom. \n\nDetta är troligen ett kosmetiskt fel som introducerades i [https://github.com/Timewave-AB/timewave/pull/1320|https://github.com/Timewave-AB/timewave/pull/1320|smart-link]  där vi började använda en Laravel 8 service för att anropa SMS servicen. Som den tjänsten var skriven så returnerade den hela response objektet på HTTP-anropet och därmed kunde Laravel 3 koden inte matcha det som kom tillbaka mot “kända svar” = meddelandet togs aldrig bort. Det saknades också return statements så svaret kom alltid tillbaka som “Missing ID”. \n\nJag har manuellt soft-deletat meddelandet i videon även om den förmodligen aldrig hade skickats pga sms servicen såg bra ut. ", "GroupLevel": null, "CreatedDate": "2025-07-03T11:52:44.107+02:00"}], "Attachments": [{"Id": "36711", "AuthorUser": "<PERSON>", "CreatedDate": "2025-07-03T11:02:57.61+02:00", "FileName": "Screen Recording 2025-07-03 at 11.00.09.mov", "MimeType": "video/quicktime", "FileSize": 5779595, "ContentUri": "https://timewaveab.atlassian.net/secure/attachment/36711/Screen Recording 2025-07-03 at 11.00.09.mov"}]}