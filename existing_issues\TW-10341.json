{"Key": "TW-10341", "Id": "50469", "Summary": "kundportalen: medge visning av (enbart) förnamn på anställda", "Description": "h1. Bakgrund\n\nVi har en kundportal.\n\nI denna visas kundernas kommande (och utförda bokningar).\n\nVåra kunder kan välja att visa de anställdas namn vid uppdragen i Kundportalen.\n\nIdag kan vår kunder välja på att ha antingen:\n\n* hela namnet synlig (för och efternamn)\n* dölja namnet helt\n\nh1. Problem\n\nVåra kunder vill visa namn på utförare men inte exponera de anställdas efternamn.\n\nh1. Lösning\n\n*Vi medger valen:*\n\n* <PERSON>ö<PERSON>j anställdas namn\n* Visa anställdas förnamn\n* Visa anställdas för- och efternamn\n\n\n\n\nh1. Acceptanskriterier\n\nI inställningarna för kundportalen finns alternativen\n\n* Dölj anställdas namn\n* Visa anställdas förnamn\n* Visa anställdas för- och efternamn\n\nEndast en av ovan tre ska kunna väljas\n\n“Visa anställdas för- och efternamn” ska vara satt för kunder som nu har alternativet visa namn\n\n“Dölj anställds namn” ska vara satt för kunder som nu har inställt att inte visa namn\n\nUtifrån satt inställning ska det överallt en anställds namn visas i kundportalen endast visas det som är valt. Om förnamn och efternamn inte är separerat, visa endast alla tecken fram till första mellanslaget om valet “Visa anställds förnamn” är valt", "Assignee": "<PERSON>", "Reporter": "anders.backman", "Labels": ["after-summer-2", "approved-not-merged", "kundportalen"], "CreateDate": "2025-05-23T18:01:19.387+02:00", "UpdateDate": "2025-07-14T14:48:38.221+02:00", "Status": "In Testing", "ParentIssueKey": "TW-9429", "IssueType": "Feature", "Priority": null, "ResolutionDate": null, "Resolution": null, "Links": [], "Comments": [], "Attachments": []}