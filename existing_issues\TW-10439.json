{"Key": "TW-10439", "Id": "50875", "Summary": "Kundportalen: medge tak på visade timma i timpott", "Description": "h1. Bakgrund\n\nVi har en Kundportal.\n\nI kundportalen ser kunderna (om vår tenant vald det) att kundens timpottssaldo skall visas för slutkund under “<PERSON><PERSON> konto”.\n\nVissa tenants visar kundernas timpott på fakturor och ev. även i kundportal.\n\nVissa tenants har valt att inte visa timpott utan meddelar denna till kund om kund efterfrågar den.\n\nVissa kunder har ett maxtax för hur många timmar en kund får spara. T.ex. max 4 timmar. Kunden har då två alternativ:\n\nInför varje fakturering redigera kundernas timpott så att timpotssaldot vid faktureringen ej överstiger 4 timmar. Är praktiskt helt omöjlig (så egentligen inget alternativ när jag tänker efter).\n\nEller att helt dölja timpottsaldot för kund. Så om kund har t.ex. 8 timmar och efterfr<PERSON><PERSON> timpottssaldo som meddelas helt enkelt 4 timmar.\n\n\nh1. Problem\n\nVissa kunder har ett maxtax för hur många timmar en kund får spara. T.ex. max 4 timmar. Tenant har då två alternativ:\n\n# Inför varje fakturering redigera kundernas timpott så att timpotssaldot vid faktureringen ej överstiger 4 timmar. Är praktiskt helt omöjlig (så egentligen inget alternativ när jag tänker efter).\n\n# Eller att helt dölja timpottsaldot för kund. Så om kund har t.ex. 8 timmar och efterfrågar timpottssaldo som meddelas helt enkelt 4 timmar.\n\nh1. Lösning\n\nGenom:\n\n1) att medge kunden åtkomst till timpottssaldo (enbart) i kundportal, och..\n\n2) att kundportalen aldrig visar ett värde överstigande tenants maxtax, så..\n\n3) .. kommer tenant att kunne ha ett praktiskt maxtax, samtidigt som den korrekta timpotten återfinns i systemet, vilket är bra.\n\nLösningen implementeras så att i backoffice anges max antal timmar att visa:\n\n!Skärmavbild 2025-06-05 kl. 13.55.17.png|width=515,height=293,alt=\"Skärmavbild 2025-06-05 kl. 13.55.17.png\"!\n\nLogiken är sedan att om kunden har anget “4” som tak så visas alltid max 4 timmar, ex:\n\n\n||*Faktisk timpott*||*Visad timpott*||\n|-2,5|-2,5|\n|0|0|\n|1,4|1,4|\n|2,5|2,5|\n|3,9|3,9|\n|4,0|4,0|\n|4,3|4,0|\n|6|4,0|\n\nh1. \n*Acceptanskriterier*\n\nDet finns ett fält i admininställningarna för kundportalen där ett decimalvärde större än eller lika med 0 ska kunna sättas\n\nOm ett värde är satt, ska timbanken visas med villkoret MIN(faktiskt_timbanksvärde, satt_inställning)\n\nOm värde i ovan inställning saknas, eller är 0, ska ingen ändring i visning göras, och faktiskt_timbanksvärde alltid visas\n\nMaxvärdet ska kunna sättas med 2 decimaler", "Assignee": "<PERSON>", "Reporter": "anders.backman", "Labels": ["after-summer-2", "approved-not-merged", "kundportalen"], "CreateDate": "2025-06-05T13:40:34.365+02:00", "UpdateDate": "2025-07-14T15:14:58.123+02:00", "Status": "In Review", "ParentIssueKey": "TW-9429", "IssueType": "Feature", "Priority": null, "ResolutionDate": null, "Resolution": null, "Links": [{"LinkType": "<PERSON><PERSON><PERSON>", "InwardIssueKey": "TW-10439", "OutwardIssueKey": "TW-10380"}, {"LinkType": "Blocks", "InwardIssueKey": "TW-10579", "OutwardIssueKey": "TW-10439"}], "Comments": [{"Id": "69946", "AuthorUser": "<PERSON>", "Body": "Chainad på PR för [https://timewaveab.atlassian.net/browse/TW-10579|https://timewaveab.atlassian.net/browse/TW-10579|smart-link] iom ändringarna överlappar.", "GroupLevel": null, "CreatedDate": "2025-06-25T14:54:28.273+02:00"}], "Attachments": [{"Id": "36118", "AuthorUser": "anders.backman", "CreatedDate": "2025-06-05T13:59:17.108+02:00", "FileName": "Skärmavbild 2025-06-05 kl. 13.55.17.png", "MimeType": "image/png", "FileSize": 76689, "ContentUri": "https://timewaveab.atlassian.net/secure/attachment/36118/Skärmavbild 2025-06-05 kl. 13.55.17.png"}]}