{"Key": "TW-10519", "Id": "51150", "Summary": "api-4: 500 på PUT issues", "Description": "h1. Bakgrund\n\nPUT /v4/issues/********-6ec0-78a8-9911-bdd38a40e552\n\n{noformat}{\n    \"id\": \"********-6ec0-78a8-9911-bdd38a40e552\",\n    \"subject\": \"hej\",\n    \"content\": \"hej2\",\n    \"categoryIds\": [\n        {\n            \"id\": \"01902b75-4be4-73a3-8bcf-703e4a613af3\"\n        }\n    ],\n    \"statusId\": \"01904a50-0d78-722c-824b-276351e451f7\",\n    \"createdByAccountId\": \"********-11ab-72ec-b9f6-6991adfd01db\"\n}{noformat}\n\nAlla UUIDv7 var vid tiden gällande\n\nh1. Problem\n\n500\n\n[https://twstaging.grafana.net/goto/3vZGkeLHg?orgId=1|https://twstaging.grafana.net/goto/3vZGkeLHg?orgId=1]\n\nh1. Lösning\n\n\n\nh1. Acceptanskriterier\n\nIstället för 500 får man ett förklarande fel (något fält saknas, fel behörighet osv) för detta specifika fall", "Assignee": "<PERSON>", "Reporter": "<PERSON>", "Labels": ["after-summer-2", "api-4", "approved-not-merged"], "CreateDate": "2025-06-16T13:45:17.153+02:00", "UpdateDate": "2025-07-14T11:37:13.378+02:00", "Status": "In Review", "ParentIssueKey": "TW-10664", "IssueType": "Feature", "Priority": null, "ResolutionDate": null, "Resolution": null, "Links": [], "Comments": [{"Id": "70019", "AuthorUser": "<PERSON>", "Body": "categoryIds skall vara en array av strings. Har justerat valideringslogiken så att det inte smäller. ", "GroupLevel": null, "CreatedDate": "2025-06-27T14:50:53.942+02:00"}], "Attachments": []}