{"Key": "TW-7032", "Id": "41621", "Summary": "utils: <PERSON><PERSON>ony<PERSON>, <PERSON><PERSON> inte tomma, icke-null-v<PERSON><PERSON>.", "Description": "h2. Bakgrund\n\nVi har en lösning som kopierar över en valfri kunds databas till staging och anonymiserar känslig data. Detta används internt av oss för test.\n\nh1. Problem \n\nVäldigt många fält innehåller bara en tom sträng, men anonymiseringsscriptet kontrollerar endast om det är NULL eller inte.\n\nh1. Lösning\n\nFörutom att kontrollera NULL, kontrollera också om värdet är en tom sträng och sätt då inte randomizad data.\n\nh1. Reproduktionssteg (krävs)\n\n* Sätt kolumnerna {{workphone}} och {{mobile}} till tomma strängar i originaldatabasen\n* Anonymisera databasen till staging\n* {{workphone}} och {{mobile}} får fake:ade nummer i sig\n\nh1. Acceptanskriterier (Testplan)\n\n* Kör dbanon-binären på en lokal databas och kontrollera att ett fält, t ex “arbetstelefon”, inte fått något värde om det tidigare hade en tom sträng.\n* Kontrollera efter produktionssättning att det även fungerar när github-pipelinen körs.", "Assignee": "<PERSON><PERSON><PERSON>", "Reporter": "<PERSON><PERSON><PERSON>", "Labels": ["utils"], "CreateDate": "2023-09-06T16:53:23.38+02:00", "UpdateDate": "2025-07-14T09:09:29.141+02:00", "Status": "In Testing", "ParentIssueKey": "TW-10626", "IssueType": "Feature", "Priority": null, "ResolutionDate": null, "Resolution": null, "Links": [], "Comments": [], "Attachments": []}