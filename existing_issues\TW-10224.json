{"Key": "TW-10224", "Id": "50084", "Summary": "tenant-web-ui-3: <PERSON><PERSON> f<PERSON> inte med till kopierad faktura", "Description": "h1. Bakgrund\n\n\n\nh1. Rapporterad av\n\nChristian\n\nh1. Förväntat beteende\n\nNär en faktura kopieras ska all relevant information inklusive adressinformation följa med till den nya fakturan.\n\nh1. Faktiskt beteende\n\nAdressinformation följer inte med när en faktura kopieras, vilket innebär att användaren måste fylla i adressen manuellt igen.\n\nh1. Reproduktionssteg\n\n# Gå till fakturahantering i tenant-web-ui-3\n# Välj en befintlig faktura med adressinformation\n# <PERSON>licka på \"Kopiera faktura\"\n# Kontrollera den nya fakturan\n# Observera att adressinformation saknas\n\nh1. Miljö\n\n* System:\n** tenant-web-ui-3\n\nh1. Lösning\n\nSäkerställ att kopieringsfunktionen inkluderar alla adressfält när en faktura kopieras.", "Assignee": "<PERSON>", "Reporter": "<PERSON>", "Labels": ["tenant-web-ui-3"], "CreateDate": "2025-05-15T16:00:41.597+02:00", "UpdateDate": "2025-07-15T13:50:41.855+02:00", "Status": "Done", "ParentIssueKey": "TW-10625", "IssueType": "Bug", "Priority": null, "ResolutionDate": "2025-07-15T13:50:40.575+02:00", "Resolution": "Done", "Links": [], "Comments": [{"Id": "70078", "AuthorUser": "<PERSON>", "Body": "[~accountid:712020:a4d3cb70-8b27-40ca-a267-546f64d83993]  Jag tror inte detta är generellt, så det behövs nog ett explicit exempel. \n\n!Skärminspelning 2025-06-30 kl. 07.47.37.mov|width=2548,height=1440,alt=\"Skärminspelning 2025-06-30 kl. 07.47.37.mov\"!", "GroupLevel": null, "CreatedDate": "2025-06-30T07:49:15.033+02:00"}, {"Id": "70079", "AuthorUser": "<PERSON>", "Body": "[~accountid:63d7e3de614cb4ba53fdd853]ok\n\nSka kolla. Kanske får vänta på att Christian är tillbaka som rapporterade det ", "GroupLevel": null, "CreatedDate": "2025-06-30T08:20:19.256+02:00"}, {"Id": "70442", "AuthorUser": "<PERSON>", "Body": "[~accountid:5ee9cbfbb04ccf0aae6acf92] får gärna återkomma med specifikt exempel", "GroupLevel": null, "CreatedDate": "2025-07-04T17:40:21.593+02:00"}, {"Id": "70672", "AuthorUser": "Automation for Jira", "Body": "Reason for rejection: Å<PERSON>upptar om problemet dyker upp igen", "GroupLevel": null, "CreatedDate": "2025-07-15T13:50:41.61+02:00"}], "Attachments": [{"Id": "36612", "AuthorUser": "<PERSON>", "CreatedDate": "2025-06-30T07:49:14.685+02:00", "FileName": "Skärminspelning 2025-06-30 kl. 07.47.37.mov", "MimeType": "video/quicktime", "FileSize": ********, "ContentUri": "https://timewaveab.atlassian.net/secure/attachment/36612/Skärminspelning 2025-06-30 kl. 07.47.37.mov"}]}