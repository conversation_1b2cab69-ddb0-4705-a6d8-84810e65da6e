{"Key": "TW-10579", "Id": "51538", "Summary": "Kundportalen: <PERSON><PERSON> hur vi jobbar med properties på \"nuvarande företag\"", "Description": "h1. Bakgrund\n\nKundportalen är en multi-tenant application. När portalen körs så resolvar vi det “aktuella företaget” via den subdomän som används för att accessa portalen. Företaget resolvas en gång i uppstarten av varje request och görs sedan tillgänglig för controllers/services och för alla blade vyer i systemet för easy access. \n\nh1. Problem\n\nDet finns en del data som är “känslig” på företaget (hemlighet för API-token osv) som aldrig skall vara tillgänglig “överallt” för att vara ultra-deluxe-säker på att vi inte läcker den. Just nu gör vi detta genom att explicit selecta all data som skall vara tillgänglig vilket gör att när vi lägger till ny data på ett företag måste vi på flera ställen komma ihåg att inkludera datat. Vi borde istället jobba precis tvärtom och explicit dölja den känsliga datan för att underlätta implementation av ny data och minska risken för mänskliga fel.\n\nh1. Acceptanskriterier\n\n* Portalens kod har justerats så att {{client_id}} och {{client_secret}} som används för att ställa ut tokens via API-3 är skyddade och aldrig är tillgängliga i blade vyer\n* Den “administrativa bördan” av att lägga till nya kolumner på det globala {{company}} objektet är minskad.\n* Kundportalens nuvarande funktionalitet inte har påverkats. ", "Assignee": "<PERSON>", "Reporter": "<PERSON>", "Labels": ["after-summer-2", "approved-not-merged", "kundportalen"], "CreateDate": "2025-06-25T11:02:20.357+02:00", "UpdateDate": "2025-07-11T15:52:22.219+02:00", "Status": "In Review", "ParentIssueKey": "TW-9429", "IssueType": "Chore", "Priority": null, "ResolutionDate": null, "Resolution": null, "Links": [{"LinkType": "Blocks", "InwardIssueKey": "TW-10579", "OutwardIssueKey": "TW-10439"}, {"LinkType": "Blocks", "InwardIssueKey": "TW-10579", "OutwardIssueKey": "TW-10342"}, {"LinkType": "Blocks", "InwardIssueKey": "TW-10579", "OutwardIssueKey": "TW-10380"}, {"LinkType": "Blocks", "InwardIssueKey": "TW-10579", "OutwardIssueKey": "TW-10425"}], "Comments": [], "Attachments": []}