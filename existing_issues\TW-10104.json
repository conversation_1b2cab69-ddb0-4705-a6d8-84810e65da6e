{"Key": "TW-10104", "Id": "49669", "Summary": "TWU3: Trimma datafält, e-post, adresser etc. vid input", "Description": "h1. Bakgrund\n\nVi har massa fält som det ligger logik på eller som används som id externt.\n\nGäller endast laravel 3, i laravel 8 görs detta automatiskt.\n\nh1. Problem\n\nAnvändare matar in skräp\n\nh1. Lösning\n\nTrimma alla whitespaces på relevanta fält vid sparande, d.v.s. vid input\n\nh1. Acceptanskriterier\n\nLikt [https://timewaveab.atlassian.net/browse/TW-10099|https://timewaveab.atlassian.net/browse/TW-10099|smart-link] , trimma följande fält:\n\nDessa fält i kundkort och anställda:\n\n* Adressfält\n* Förnamn\n* Efternamn\n* Namn\n* Organisationsnummer\n* Personnummer\n* Ort\n* Telefonnummer", "Assignee": "<PERSON>", "Reporter": "<PERSON>", "Labels": ["after-summer-2", "approved-not-merged", "tenant-web-ui-3"], "CreateDate": "2025-05-02T13:34:34.878+02:00", "UpdateDate": "2025-07-15T08:40:44.958+02:00", "Status": "In Review", "ParentIssueKey": "TW-10625", "IssueType": "Feature", "Priority": null, "ResolutionDate": null, "Resolution": null, "Links": [{"LinkType": "Relates", "InwardIssueKey": "TW-10099", "OutwardIssueKey": "TW-10104"}, {"LinkType": "Blocks", "InwardIssueKey": "TW-10104", "OutwardIssueKey": "TW-10329"}], "Comments": [{"Id": "68362", "AuthorUser": "<PERSON>", "Body": "Fälten i företagsinställningar viktiga, skräp i dessa fält hindrar diverse utskick.", "GroupLevel": null, "CreatedDate": "2025-05-07T05:46:57.387+02:00"}, {"Id": "69887", "AuthorUser": "<PERSON>", "Body": "Har trimmat på formuläret för anställda. För kunder ser det ut som att det redan är trimmat på:\n\n* /clients/edit\n* /clients/new\n* /clients/new/address\n* /clients/edit/address/120\n\nOm det är ett känt problem med kunder någonstans behöver det specificeras vart det inträffar. ", "GroupLevel": null, "CreatedDate": "2025-06-24T11:23:58.039+02:00"}], "Attachments": []}