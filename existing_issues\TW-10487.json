{"Key": "TW-10487", "Id": "51027", "Summary": "tenant-web-ui-3: <PERSON><PERSON> a<PERSON> (och andra fel)", "Description": "h1. Bakgrund\n\nVi använder ansvarfinans/PSfinance för att kolla persnr.\n\nh1. Problem\n\nVi får ibland statuskoder som vi inte kan hantera från APIet\n\n{noformat}curl --location 'https://apifinance2.pssync.com/v1/addresses?iscompany=false&country=SE&securitynumber=19370605-3703' \\\n--header 'Token: TOKEN' \\\n--header 'Apikey: APIKEY' \\\n--header 'Secret: SECRET'{noformat}\n\nGer svaret\n\n{code:json}{\n    \"Error\": {\n        \"Code\": \"00\",\n        \"Message\": \"Avliden (2025-02-17)\"\n    }\n}{code}\n\n\n\nh1. Lösning\n\nVisa meddelandet vi får från APIet för användaren istället för att molnet bara ska snurra.\n\nh1. Acceptanskriterier\n\nNär man trycker på molnet på anställdkortet:\n\n* Om allt ok->Fyll i info\n* Om 200 med error body, visa data.Error.Message i en notisruta likt andra fel<PERSON>", "Assignee": "<PERSON>", "Reporter": "<PERSON>", "Labels": ["after-summer-2", "approved-not-merged", "tenant-web-ui-3"], "CreateDate": "2025-06-11T14:10:57.975+02:00", "UpdateDate": "2025-07-15T09:13:11.201+02:00", "Status": "In Review", "ParentIssueKey": "TW-10625", "IssueType": "Feature", "Priority": null, "ResolutionDate": null, "Resolution": null, "Links": [{"LinkType": "Relates", "InwardIssueKey": "TW-10487", "OutwardIssueKey": "TW-8974"}], "Comments": [{"Id": "69657", "AuthorUser": "<PERSON>", "Body": "<PERSON><PERSON> <PERSON>,\n\n<PERSON><PERSON> bra. <PERSON><PERSON> skulle rekommendera att alltid kontrollera ifall”Error” inte är tomt som ett första steg.\n\nSamtliga orsaker nedan grupperas alla under ”Code”: 00.\n\nAvliden,\n\nSp<PERSON><PERSON>d,\n\nUtvandrad,\n\nPersonnummerbyte,\n\nTekniskt avregistrerad\n\n \n\nIfall det därutöver finns mer info – exempelvis ett datum när något har inträffat står det inom parentesen som en kommentar.\n\nMen det är inte garanterat att sådan information existerar och dessa meddelanden bör ses som bakgrundslogg.\n\nHa en fortsatt bra dag!\n\nMed vänliga hälsningar / Best Regards\n\n \n\n \n\n\tERIC WINTER\n\nDEPUTY CEO", "GroupLevel": null, "CreatedDate": "2025-06-11T16:10:08.537+02:00"}], "Attachments": []}