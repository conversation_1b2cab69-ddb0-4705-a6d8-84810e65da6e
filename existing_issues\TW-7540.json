{"Key": "TW-7540", "Id": "42130", "Summary": "Larm på staging-VM:s metrics", "Description": "h1. Bakgrund\n\nVi kör ett gäng VM:s på VMWare på fyra fysiska servrar. Varje VM kör Opentelemetry som i sin tur levererar metrics till Uptrace.\n\nh1. Problem\n\nUptrace har några få standard-larm inställda, men dom är inte adekvata för oss.\n\nh1. Acceptanskriterier\n\n* Om en staging-VM har över 75% CPU util över 10min, skicka larm till Slack, Teams staging-<NAME_EMAIL>.\n* Om en staging-VM har över 75% RAM util över 10min, skicka larm till Slack, Teams <NAME_EMAIL>.\n* Om en staging-VM har mindre än 10GB ledigt på / eller någon annan mountad volym, över 10 min, skicka larm till Slack, Teams staging-<NAME_EMAIL>.\n* Meddelandet till slacks staging-kanal skall trigga SMS till dom som vill ha det.", "Assignee": "<PERSON><PERSON><PERSON>", "Reporter": "<PERSON><PERSON><PERSON>", "Labels": ["infra"], "CreateDate": "2023-12-17T22:57:10.136+01:00", "UpdateDate": "2025-07-14T15:45:22.922+02:00", "Status": "In Review", "ParentIssueKey": "TW-10663", "IssueType": "Feature", "Priority": null, "ResolutionDate": null, "Resolution": null, "Links": [{"LinkType": "Blocks", "InwardIssueKey": "TW-7542", "OutwardIssueKey": "TW-7540"}], "Comments": [{"Id": "70640", "AuthorUser": "<PERSON><PERSON><PERSON>", "Body": "<PERSON>i skickar inte lika mycket ö<PERSON>vakning på från staging som från prod (det borde vi nog ändra), men den här går inte att lösa ut utan att också ändra på hur nodesetupen för övervakning ser ut på staging. Kan tycka att vi inte heller behöver ha metricslarm på staging på samma sätt som på prod heller…", "GroupLevel": null, "CreatedDate": "2025-07-14T15:45:20.952+02:00"}], "Attachments": []}