{"Key": "TW-10598", "Id": "51613", "Summary": "API-3: <PERSON><PERSON><PERSON> filtrering av \"har anställd\" på GET /missions", "Description": "", "Assignee": "<PERSON>", "Reporter": "<PERSON>", "Labels": ["after-summer-2", "approved-not-merged"], "CreateDate": "2025-06-26T15:47:28.585+02:00", "UpdateDate": "2025-07-11T16:01:34.518+02:00", "Status": "In Review", "ParentIssueKey": "TW-10380", "IssueType": "Sub-task", "Priority": null, "ResolutionDate": null, "Resolution": null, "Links": [{"LinkType": "Blocks", "InwardIssueKey": "TW-10598", "OutwardIssueKey": "TW-10380"}], "Comments": [{"Id": "69981", "AuthorUser": "<PERSON>", "Body": "{noformat}GET /missions?filter[has_employee]=1|0{noformat}\n\n0 = Har inte anställd på sig / är kopplad till “Utan anställd” (employee_id = 0)\n\n1 = Har inte “Utan anställd” på sig (employee_id != 0)", "GroupLevel": null, "CreatedDate": "2025-06-26T16:09:39.424+02:00"}], "Attachments": []}