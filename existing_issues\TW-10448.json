{"Key": "TW-10448", "Id": "50895", "Summary": "TWU4: Förbättra CSS-klassnamn", "Description": "h1. Bakgrund\n\nFör att förenkla testning och läsbarhet i vår markup använder vi sematisk namngivning. Bra exempel och recap: [https://maintainablecss.com/chapters/semantics/|https://maintainablecss.com/chapters/semantics/] \n\nh1. Problem\n\nVi har en drös icke-semantiska klassnamn i vår HTML i TWU4 redan. T ex “vertical-spacing”, “overflow-x”.\n\nh1. Acceptanskriterier\n\n* Inga icke semantiska klassnamn finns kvar i TWU4.", "Assignee": "<PERSON>", "Reporter": "<PERSON><PERSON><PERSON>", "Labels": ["after-summer-2", "approved-not-merged", "tenant-web-ui-4"], "CreateDate": "2025-06-06T21:13:00.119+02:00", "UpdateDate": "2025-07-14T11:01:52.198+02:00", "Status": "In Review", "ParentIssueKey": "TW-10314", "IssueType": "Feature", "Priority": null, "ResolutionDate": null, "Resolution": null, "Links": [], "Comments": [], "Attachments": []}