{"Key": "TW-10533", "Id": "51226", "Summary": "app: Sentryfel \"overused RAM\"", "Description": "h1. Bakgrund\n\n\n\nh1. Problem\n\n[https://timewave-ab.sentry.io/issues/6685313291/events/cd6432a0360b4e14aacb4d42ab46f71e/|https://timewave-ab.sentry.io/issues/6685313291/events/cd6432a0360b4e14aacb4d42ab46f71e/|smart-link] \n\nDet jättekonstiga är (om det stämmer att det är app 0.8.15 och iOS) att felet kom innan versionen blev produktionssatt i App Store.\n\nh1. Lösning\n\n\n\nh1. Acceptanskriterier\n\nDet är utklurat vad som är fel, och en issue för att lösa problemet är skapat om det inte är en one off.", "Assignee": "<PERSON>", "Reporter": "<PERSON>", "Labels": ["after-summer-2", "app", "approved-not-merged"], "CreateDate": "2025-06-17T12:03:29.628+02:00", "UpdateDate": "2025-07-14T13:14:35.738+02:00", "Status": "In Review", "ParentIssueKey": "TW-10671", "IssueType": "Feature", "Priority": null, "ResolutionDate": null, "Resolution": null, "Links": [], "Comments": [{"Id": "70081", "AuthorUser": "<PERSON>", "Body": "<PERSON>ta verkar ju legit vara Apple själva under testprocessen av appen givet:\n\n{noformat}Geography\nCupertino, United States (US)\nID\n7FD59D2D-539E-43D6-9CFD-72B0165704F4\nIP Address\n139.178.131.14{noformat}\n\n{noformat}inetnum:        ************* - ***************\nnetname:        LEGACO-139-178-128-0\ndescr:          Apple Inc.\ncountry:        US\nadmin-c:        JD9555-RIPE\ntech-c:         JD9555-RIPE\nstatus:         LEGACY\nmnt-by:         APPLE-MNT\ncreated:        2018-09-28T12:00:55Z\nlast-modified:  2019-11-15T09:41:30Z\nsource:         RIPE\n\nperson:         <PERSON>\naddress:        1 Infinite Loop\naddress:        Cupertino, CA 95051\naddress:        USA\nphone:          ****** 270 9195\nnic-hdl:        JD9555-RIPE\nmnt-by:         APPLE-MNT\ncreated:        2017-04-18T14:41:16Z\nlast-modified:  2019-02-16T16:25:23Z\nsource:         RIPE # Filtered{noformat}", "GroupLevel": null, "CreatedDate": "2025-06-30T10:28:05.836+02:00"}, {"Id": "70082", "AuthorUser": "<PERSON>", "Body": "Det ser ut som att Apple har testat att “spam-klicka” på en knapp i appen. Denna knapp använde useState för att skydda sig mot multipla klick men som vi vet vid det här laget är state async i React och det finns därför ett litet fönster mellan första knapptrycket och då React verkligen uppdaterar statet och sen renderar om UI:t där flera klick skulle kunna smyga in. Det ser ut som att detta är “programmatiskt testat” där de har skickat in massa klick snabbt och för varje klick skapar vi en AbortController med en 30 sek timeout på följande API-anrop och förmodligen har dessa köat upp tillräckligt så att minnet tagit slut. \n\nJag har lagt till en ref - som är sync - som skyddar API-anropet så det inte skall vara möjligt att få till detta. Om detta faktiskt är problemet är det inte speciellt sannolikt att “vanliga” användare råkar ut för det men nu skall förhoppningsvis inte ens the Californa hotshots göra det heller. ", "GroupLevel": null, "CreatedDate": "2025-06-30T11:10:24.621+02:00"}], "Attachments": []}