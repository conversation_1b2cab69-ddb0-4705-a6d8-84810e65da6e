{"Key": "TW-7194", "Id": "41783", "Summary": "kundportalen: Larm vid ärendeansvarig som inte är aktiv i TimeWave-domän", "Description": "h1. *Bakgrund*\n\nKundportalen slutade ta emot meddelanden (ärenden) i en kunds Kundportal utan att någon förstod det.\n\nDe fick dagliga meddelanden fram till i maj, och sedan var det helt tyst över ett kvartal utan att vi förstod något var fel.\n\nDet visade sig att Kundportalen inte mot användare frontar att något är fel, och slänger alla meddelanden som kommer utan åtgärd i tysthet.\n\nh1. *Problem*\n\nVia adminportalen [https://admin.twportal.se/home|https://admin.twportal.se/home] ska en ärendeansvarig anges för alla Kundportaler. Dessa ärendeansvariga taggas inne i TimeWave som skapare av alla ärenden via Kundportalen.\n\nNär de inaktiveras i TimeWave slutar portalen fungera, men inget flaggas någonstans.\n\n!image-20231009-071400.png|width=590,height=232!\n\nh1. *Lösning* \n\nLarma när ärendeansvarig ligger på domän i Kundportalen som inte finns som aktiv anställd i motsvarande TimeWave-domän.\n\nh1. *Acceptanskriterier*\n\n* Det skall komma ett larm till [<EMAIL>|mailto:<EMAIL>] när en ärendeansvarig i kundportalen slutar vara aktiv anställd i motsvarande Timewave-domän.\n* När det här händer, skicka med _all_ data från formuläret som gått förlorad till [<EMAIL>|mailto:<EMAIL>] .\n* Mailet ska skickas varjet gång formuläret fylls i och det smäller, oavsett tidigare resultat", "Assignee": "<PERSON>", "Reporter": "<PERSON>", "Labels": ["after-summer-2", "approved-not-merged", "kundportalen"], "CreateDate": "2023-10-09T09:01:40.489+02:00", "UpdateDate": "2025-07-14T14:56:28.97+02:00", "Status": "In Review", "ParentIssueKey": "TW-9429", "IssueType": "Chore", "Priority": null, "ResolutionDate": null, "Resolution": null, "Links": [{"LinkType": "Relates", "InwardIssueKey": "TW-7194", "OutwardIssueKey": "TW-8791"}], "Comments": [{"Id": "69947", "AuthorUser": "<PERSON>", "Body": "<PERSON><PERSON>r pausa lite med denna eftersom PECL servern verkar vara helt nuke:d så det går inte att få in redis-paketet för PHP just nu (och jag behöver av oförklarlig anledning köra docker system prune mellan varje PR jag gör i portalen). Det går inte ens att gå till [https://pecl.php.net/|https://pecl.php.net/|smart-link]  och söka utan att det smäller, så får vackert vänta på att det kommer upp igen. ", "GroupLevel": null, "CreatedDate": "2025-06-25T15:28:21.335+02:00"}, {"Id": "69952", "AuthorUser": "<PERSON>", "Body": "Det fanns redan kod som skickade ut ett mail när detta inträffade, så jag har utökat detta med att även innehålla information om användaren + rubrik + innehåll:\n\n!Skärmavbild 2025-06-25 kl. 16.53.26.png|width=666,height=525,alt=\"Skärmavbild 2025-06-25 kl. 16.53.26.png\"!", "GroupLevel": null, "CreatedDate": "2025-06-25T16:55:35.245+02:00"}], "Attachments": [{"Id": "28935", "AuthorUser": "<PERSON>", "CreatedDate": "2023-10-09T09:17:26.665+02:00", "FileName": "image-20231009-071400.png", "MimeType": "image/png", "FileSize": 12470, "ContentUri": "https://timewaveab.atlassian.net/secure/attachment/28935/image-20231009-071400.png"}, {"Id": "36546", "AuthorUser": "<PERSON>", "CreatedDate": "2025-06-25T16:55:34.945+02:00", "FileName": "Skärmavbild 2025-06-25 kl. 16.53.26.png", "MimeType": "image/png", "FileSize": 49346, "ContentUri": "https://timewaveab.atlassian.net/secure/attachment/36546/Skärmavbild 2025-06-25 kl. 16.53.26.png"}]}