{"Key": "TW-10291", "Id": "50329", "Summary": "TWU-3: Visa inte rabattinfo om den är 0%", "Description": "h1. Rapporterad av\n\nHemrex\n\nh1. Förväntat beteende\n\nI fakturavisningen finns en kolumn för rabatter. Denna kolumn ska endast visas om:\n\n# Inställningen *\"Visa rabatter\"* är aktiverad, och\n# Det finns minst en rad på fakturan som har en rabatt angiven.\n\nh1. Faktiskt beteende\n\nI fakturavisningen finns en kolumn för rabatter. Denna kolumn visas alltid, även om det inte finns någon rabatt på fakturan.\n\nh1. Reproduktionssteg\n\n# Gå till en faktura som inte har några rabatter.\n# Säkerställ att inställningen _Visa rabatter_ är avstängd.\n# Öppna fakturavisningen.\n# Rabattkolumnen visas trots att den inte borde.\n\nh1. Miljö\n\n* System\n** tenant-web-ui-3\n\nh1. Screenshots/inspelningar\n\n!Screenshot from 2025-07-07 10-56-05.png|width=3828,height=1812,alt=\"Screenshot from 2025-07-07 10-56-05.png\"!\n\n\n\nh1. Lösning\n\nJustera visningslogiken för rabattkolumnen så att:\n\n✅ *Kolumnen ska visas om:*\n\n* Inställningen _Visa rabatter_ är aktiverad\n* Minst en fakturarad har rabatt > 0\n\n❌ *Kolumnen ska inte visas om:*\n\n* Inställningen _Visa rabatter_ är avstängd, oavsett om rabatter finns eller ej\n* Inställningen är påslagen, men ingen fakturarad har någon rabatt", "Assignee": "<PERSON><PERSON><PERSON>", "Reporter": "<PERSON>", "Labels": ["tenant-web-ui-3"], "CreateDate": "2025-05-21T14:35:25.217+02:00", "UpdateDate": "2025-07-14T15:51:00.529+02:00", "Status": "In Progress", "ParentIssueKey": "TW-10625", "IssueType": "Bug", "Priority": null, "ResolutionDate": null, "Resolution": null, "Links": [], "Comments": [{"Id": "68989", "AuthorUser": "<PERSON>", "Body": "Återkoppla att det styrs per mall på faktura. Offert går inte", "GroupLevel": null, "CreatedDate": "2025-05-22T13:54:13.108+02:00"}], "Attachments": [{"Id": "36777", "AuthorUser": "<PERSON><PERSON><PERSON>", "CreatedDate": "2025-07-07T10:56:28.275+02:00", "FileName": "Screenshot from 2025-07-07 10-56-05.png", "MimeType": "image/png", "FileSize": 309421, "ContentUri": "https://timewaveab.atlassian.net/secure/attachment/36777/Screenshot from 2025-07-07 10-56-05.png"}]}