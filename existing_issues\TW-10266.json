{"Key": "TW-10266", "Id": "50246", "Summary": "api-3: <PERSON><PERSON><PERSON> med create date vid anrop av workorder", "Description": "h1. Bakgrund\n\nBvlter bygger tillsammans med konsult ett externt BI-system. \nDetta skall bl.a. hålla reda på churn samt försäljning.\nFörsäljning aggregerad utifrån skapade arbetsordrar.\n\nSkapad-datum i TWU-3:\n\n!Skärmavbild 2025-05-19 kl. 16.41.48.png|width=1099,height=638,alt=\"Skärmavbild 2025-05-19 kl. 16.41.48.png\"!\n\nh1. Problem\n\nAPI-3 tillåter inte att filtrera AO på skapad datum.\n\nAPI-3 svar på AO innehåller inte skapd datum.\n\nh1. Lösning\n\nSkicka med create date i svaret.\n\nh1. Acceptanskriterier\n\n* Vid anrop till API-3 arbetsordrar skall create date följa med som data.", "Assignee": "<PERSON>", "Reporter": "anders.backman", "Labels": ["after-summer-2", "api-3", "approved-not-merged"], "CreateDate": "2025-05-20T09:09:23.561+02:00", "UpdateDate": "2025-07-11T16:02:36.542+02:00", "Status": "In Review", "ParentIssueKey": "TW-10625", "IssueType": "Feature", "Priority": null, "ResolutionDate": "2025-05-26T13:23:27.07+02:00", "Resolution": "Done", "Links": [{"LinkType": "Blocks", "InwardIssueKey": "TW-10438", "OutwardIssueKey": "TW-10266"}], "Comments": [{"Id": "69060", "AuthorUser": "Automation for Jira", "Body": "Reason for rejection: Workaround att använda issues\nI API-4 ser vi till att exponera alla skapandedatum", "GroupLevel": null, "CreatedDate": "2025-05-26T13:23:28.47+02:00"}, {"Id": "69456", "AuthorUser": "<PERSON>", "Body": "Jonas workaround var inte bra nog i längden\n\nÅterkom med lösning, eller kalendertidsestimat", "GroupLevel": null, "CreatedDate": "2025-06-05T13:34:32.657+02:00"}], "Attachments": [{"Id": "35589", "AuthorUser": "anders.backman", "CreatedDate": "2025-05-20T09:09:23.732+02:00", "FileName": "Skärmavbild 2025-05-19 kl. 16.41.48.png", "MimeType": "image/png", "FileSize": 707965, "ContentUri": "https://timewaveab.atlassian.net/secure/attachment/35589/Skärmavbild 2025-05-19 kl. 16.41.48.png"}]}