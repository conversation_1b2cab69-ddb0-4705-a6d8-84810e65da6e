{"Key": "TW-7541", "Id": "42131", "Summary": "Larm på sandbox-VM:s metrics", "Description": "h1. Bakgrund\n\nVi kör ett gäng VM:s på VMWare på fyra fysiska servrar. Varje VM kör Opentelemetry som i sin tur levererar metrics till Uptrace.\n\nh1. Problem\n\nUptrace har några få standard-larm inställda, men dom är inte adekvata för oss.\n\nh1. Acceptanskriterier\n\n* Om en sandbox-VM har över 75% CPU util över 10min, skicka larm till Slack, Teams sandbox-<NAME_EMAIL>.\n* Om en sandbox-VM har över 75% RAM util över 10min, skicka larm till Slack, Teams sandbox-<NAME_EMAIL>.\n* Om en sandbox-VM har mindre än 10GB ledigt på / eller någon annan mountad volym, över 10 min, skicka larm till Slack, Teams sandbox-<NAME_EMAIL>.\n* Meddelandet till slacks sandbox-kanal skall trigga SMS till dom som vill ha det.", "Assignee": "<PERSON><PERSON><PERSON>", "Reporter": "<PERSON><PERSON><PERSON>", "Labels": ["infra"], "CreateDate": "2023-12-17T22:57:17.223+01:00", "UpdateDate": "2025-07-14T15:50:49.601+02:00", "Status": "In Review", "ParentIssueKey": "TW-10663", "IssueType": "Feature", "Priority": null, "ResolutionDate": null, "Resolution": null, "Links": [{"LinkType": "Blocks", "InwardIssueKey": "TW-7542", "OutwardIssueKey": "TW-7541"}], "Comments": [{"Id": "70641", "AuthorUser": "<PERSON><PERSON><PERSON>", "Body": "Samma setup nu i Uptrace som i prod, med caveaten att sandbox-noderna inte skickar några metrics.", "GroupLevel": null, "CreatedDate": "2025-07-14T15:50:46.315+02:00"}], "Attachments": []}