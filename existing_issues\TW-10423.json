{"Key": "TW-10423", "Id": "50828", "Summary": "kundportalen: Kundportalslänkar på mitt konto går till maps.google.co.in och funkar inte", "Description": "h1. Bakgrund\n\nMan kan på vissa ställen i kundportalen klicka på en ikon vid adresser för komma till Google maps som visar adressen.\n\nh1. Rapporterad av\n\n<PERSON>\n\nh1. Förväntat beteende\n\nMan kommer till en TLD som är känd i Sverige (t.ex. .se, .com)\n\nh1. Faktiskt beteende\n\nMan kommer till .co.in\n\nh1. Reproduktionssteg\n\nKlicka på adressikonen\n\nh1. Miljö\n\n* System\n** kundportalen\n\nh1. Screenshots/inspelningar\n\n\n\nh1. Lösning\n\nOm vi kan styra det, och kan göra det med nuvarande licens för Google’s API, se till att länkarna går till, i första hand, .se, annars .com", "Assignee": "<PERSON>", "Reporter": "<PERSON>", "Labels": ["after-summer-2", "approved-not-merged", "kundportalen"], "CreateDate": "2025-06-04T13:19:36.332+02:00", "UpdateDate": "2025-07-14T14:42:11.203+02:00", "Status": "In Review", "ParentIssueKey": "TW-9429", "IssueType": "Bug", "Priority": null, "ResolutionDate": null, "Resolution": null, "Links": [], "Comments": [], "Attachments": []}