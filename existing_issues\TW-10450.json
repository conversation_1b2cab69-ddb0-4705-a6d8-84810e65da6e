{"Key": "TW-10450", "Id": "50923", "Summary": "<PERSON>era r<PERSON>ghetshanteringen på PUT /v4/bookings/{id}", "Description": "h1. Bakgrund\n\nVi har en permission vid namn {{update-order-goods-qty}}, vilken i dagsläget inte har någon faktisk funktion. Tanken var att den skulle ge rättighet att uppdatera kvantiteten på goods på en booking.\n\nI API-4 vid PUT /v4/bookings/{id} görs idag kontroller på:\n\n# Är användaren en superuser? I så fall, acceptera.\n# Om servicen som är med på bokningen har attributet “visible_mobile” = “N” OCH användaren _inte_ har en roll vid namn “administrator” så nekas skrivningen.\n# Om bokningen endast har en scheduled staff kopplat till sig och den scheduled staffens account är den anropande tokenen, acceptera.\n# Om ingen regel ovan träffats, neka skrivning.\n\nh1. Problem\n\n# {{update-order-goods-qty}} har ett namn som inte följer namnkonventionen. Dess grundidé följer inte heller våra grunder i “l<PERSON><PERSON>, “skriv”, “delete” där produkt beslutat att inte stödja “uppdatera” vid sidan av “skriv”.\n# Vi ska absolut inte använda rollens namn för att avgöra någon rättighet.\n# “visible_mobile” är en högst vansklig grund att avgöra skrivrättigheter på.\n\nh1. Acceptanskriterier\n\n# authlib o auth-4: Permissions {{bookings-write}}, {{bookings-goods-read}} och {{bookings-goods-write}} är tillagda.\n# TWU-4: Ny rad finns i UI:t för att skriva till bokningar. {{bookings-write}}, {{bookings-goods-read}} och {{bookings-goods-write}} är implementerade. (Se över namngivningen öht, bokning ska nog inte vara under uppdrag?)\n# app: Services med bookingable-by-account-roles = “employee” visas i listan över services där man kan uppdatera en booking, inga andra.\n# api-4: Auktoriseringen på PUT /v4/bookings/{id} fungerar så att:\n## Är användaren en superuser? I så fall, acceptera.\n## Har användaren permissionen {{bookings-write}}? I så fall, acceptera.\n## Har användaren permissionen {{bookings-goods-write}} och inga förändringar kommer göras på något annat än goods? I så fall, acceptera.\n## Är användaren schemalagd på bokningen? I så fall, acceptera.\n## Om ingen regel ovan träffats, neka skrivning.", "Assignee": "<PERSON>", "Reporter": "<PERSON><PERSON><PERSON>", "Labels": ["after-summer-2", "api-4", "app", "approved-not-merged", "auth-4", "tenant-web-ui-4"], "CreateDate": "2025-06-07T15:50:39.187+02:00", "UpdateDate": "2025-07-14T16:06:42.661+02:00", "Status": "In Review", "ParentIssueKey": "TW-10315", "IssueType": "Feature", "Priority": null, "ResolutionDate": null, "Resolution": null, "Links": [{"LinkType": "Blocks", "InwardIssueKey": "TW-10370", "OutwardIssueKey": "TW-10450"}], "Comments": [], "Attachments": []}