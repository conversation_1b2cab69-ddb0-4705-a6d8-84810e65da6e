{"Key": "TW-10382", "Id": "50558", "Summary": "api-4 o cron: kunna initiera ärende från TWU-3 till Kundportalen", "Description": "h1. Bakgrund\n\nVi har en kundportal.\nFrån kundportalen kan slutkund skicka in meddelanden, avbokningar och annat. Allt hamnar som ärenden i TimeWave.\nI TimeWave kan sedan vår kund lägga en kommentar på ärendet och markera den som “Visa i kundportal”. Kommentarern lägger sig då som ett svar på kundens meddelande i kundportalen. Och kommunikationen kan fortgå fram och tillbaka.\n\nh1. Problem\n\nDet finns ingen möjlighet att initiera ett ärende från TimeWave. Man kan ofta vilja kontakta en slutkund utan att slutkunden först har skickat ett meddelande\n\n_(Kan ta gift på att man kunde initiera tidigare)_ - Anders\navd. famous last words - Jonas\n\nh1. Lösning\n\nSkapa en unofficial API-4 endpoint som syncar upp kundportalens kopplingstabell med tenantdatabasernas ärenden. Skapa därefter ett cronjobb som kallar på denna endpoint var 5:e minut.\n\nSynca ärenden som har kanalen “Kundportal”.\n\nKolla hur det fungerar med ärenden som skapas från kundportalen, och se till så att det funkar likadant även om ärendet skapas från Timewave.\n\nh1. Acceptanskriterier\n\n* Ärenden skall synas i Kundportalen på samma sätt oavsett om dom är skapade i kundportalen eller i TWU-3, givet att dom har “kanal” = kundportalen.\n* Det skall vara testbart i staging.", "Assignee": "<PERSON>", "Reporter": "anders.backman", "Labels": ["api-4", "approved-not-merged", "cron"], "CreateDate": "2025-05-27T21:37:03.413+02:00", "UpdateDate": "2025-07-14T16:07:18.641+02:00", "Status": "In Review", "ParentIssueKey": "TW-9429", "IssueType": "Feature", "Priority": null, "ResolutionDate": null, "Resolution": null, "Links": [], "Comments": [], "Attachments": []}