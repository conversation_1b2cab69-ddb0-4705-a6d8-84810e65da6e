{"Key": "TW-10425", "Id": "50832", "Summary": "Tidsfrist för <PERSON>", "Description": "h1. Bakgrund\n\nStädbolag behöver kunna kontrollera när kunder kan avboka uppdrag. Olika typer av uppdrag kan ha olika avbokningsregler beroende på planering och resurser.\n\nh1. Problem\n\nKunder kan för närvarande avboka uppdrag utan tidsbegränsning, vilket kan skapa problem med schemaläggning och resurstilldelning för städbolag.\n\nh1. Lösning\n\nImplementera konfigurerbar tidsfrist för avbokningar där städföretag kan ställa in hur länge innan uppdragets start som avbokningsknappen ska visas för kunder.\n\nh1. Acceptanskriterier\n\n* Städföretag kan konfigurera tidsfrist för avbokningar (t.ex. 24 timmar innan start)\n* Avbokningsknapp visas endast inom tillåten tidsram, annars en text som konfigureras i inställningarna (t.ex. Ring 012345678 för att avboka)\n* Konfiguration kan ställas in", "Assignee": "<PERSON>", "Reporter": "<PERSON>", "Labels": ["after-summer-2", "approved-not-merged"], "CreateDate": "2025-06-04T13:58:16.643+02:00", "UpdateDate": "2025-07-11T16:06:02.932+02:00", "Status": "In Review", "ParentIssueKey": "TW-9429", "IssueType": "Feature", "Priority": null, "ResolutionDate": null, "Resolution": null, "Links": [{"LinkType": "Blocks", "InwardIssueKey": "TW-10579", "OutwardIssueKey": "TW-10425"}], "Comments": [{"Id": "70080", "AuthorUser": "<PERSON>", "Body": "Chainad PR på [https://timewaveab.atlassian.net/browse/TW-10579|https://timewaveab.atlassian.net/browse/TW-10579|smart-link] ", "GroupLevel": null, "CreatedDate": "2025-06-30T09:25:00.533+02:00"}], "Attachments": []}