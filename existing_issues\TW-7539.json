{"Key": "TW-7539", "Id": "42129", "Summary": "Larm på prod-VM:s metrics", "Description": "h1. Bakgrund\n\nVi kör ett gäng VM:s på VMWare på fyra fysiska servrar. Varje VM kör Opentelemetry som i sin tur levererar metrics till Uptrace.\n\nh1. Problem\n\nUptrace har några få standard-larm inställda, men dom är inte adekvata för oss.\n\nh1. Acceptanskriterier\n\n* Om en prod-VM har över 75% CPU util över 10min, skicka larm till Slack, Teams prod-<NAME_EMAIL>.\n* Om en prod-VM har över 75% RAM util över 10min, skicka larm till Slack, Teams prod-kanaler samt [<EMAIL>|mailto:<EMAIL>].\n* Om en prod-VM har mindre än 10GB ledigt på / *eller någon annan mountad volym*, över 10 min, skicka larm till Slack, Teams prod-kanaler samt [<EMAIL>|mailto:<EMAIL>].\n* Meddelandet till slacks prod-kanal skall trigga SMS till dom som vill ha det.", "Assignee": "<PERSON><PERSON><PERSON>", "Reporter": "<PERSON><PERSON><PERSON>", "Labels": ["infra"], "CreateDate": "2023-12-17T22:57:03.625+01:00", "UpdateDate": "2025-07-14T15:43:39.925+02:00", "Status": "In Review", "ParentIssueKey": "TW-10663", "IssueType": "Feature", "Priority": null, "ResolutionDate": null, "Resolution": null, "Links": [{"LinkType": "Blocks", "InwardIssueKey": "TW-7542", "OutwardIssueKey": "TW-7539"}], "Comments": [{"Id": "70639", "AuthorUser": "<PERSON><PERSON><PERSON>", "Body": "Jag har nu justerat så att Uptrace larmar på följande:\n\n# CPU util > 75% i prod i en period över 10 minuter\n# Mem util > 90% i prod i en period över 10 minuter\n# Disk util > 90% i prod i en period över 10 minuter\n\nLarmet går till #driftövervakning på Slack. Varje användare kan själv välja hur dom vill bli notifierade från den kanalen. SMS känns onödigt, då Slack själv kan push notisa till mobilen för den som vill det. Jag har ställt in det på min telefon iaf.\n\nMed nuvarande nod-övervakning skickar vi inte hur mycket ledigt diskutrymme det är, bara hur mycket som används, och därför är den 90% istället för 10GB som det står i acceptanskriterierna.\n\nMinnet är uppe på 90% istället för 75% då vi har strypt ner maskinerna till att stabilt använda rimligt med minne, och dom går över 75% rätt ofta utan att det är ett problem.\n\nJag har skitit fullständigt i Teams och mail. Vill man ha något dit kan man själv gå in och säga till slack att skicka dit.", "GroupLevel": null, "CreatedDate": "2025-07-14T15:43:28.747+02:00"}], "Attachments": []}